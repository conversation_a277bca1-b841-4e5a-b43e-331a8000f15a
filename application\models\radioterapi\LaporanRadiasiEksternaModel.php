<?php
defined('BASEPATH') or exit('No direct script access allowed');

class LaporanRadiasiEksternaModel extends CI_Model
{
    var $table = 'medis.radiasi_eksterna';
    var $column_order = array(null, 'lokasi', 'catatan_simulator', 'oleh', 'waktu');
    var $column_search = array('lokasi', 'catatan_simulator', 'oleh', 'waktu');
    var $order = array('id' => 'asc');

    private function _get_datatables_query($nomr)
    {
        $this->db->select([
            'medis.radiasi_eksterna.*',
            'CASE
                WHEN medis.radiasi_eksterna.category = "1" THEN CONCAT("CT - ", medis.tb_ctSimulatorDokter.deskripsi)
                WHEN medis.radiasi_eksterna.category = "2" THEN CONCAT("SK - ", medis.tb_simulatorInformationDr.diagnosa)
                ELSE "Tidak ada catatan"
            END as catatan_simulator'
        ]);
        $this->db->from($this->table);
        $this->db->join('pendaftaran.kunjungan', 'pendaftaran.kunjungan.NOMOR = medis.radiasi_eksterna.nokun', 'left');
        $this->db->join('pendaftaran.pendaftaran', 'pendaftaran.pendaftaran.NOMOR = pendaftaran.kunjungan.NOPEN', 'left');

        // Join dengan tabel CT Simulator
        $this->db->join('medis.tb_ctSimulatorDokter',
            'medis.tb_ctSimulatorDokter.id = medis.radiasi_eksterna.id_ctsim AND medis.radiasi_eksterna.category = "1"',
            'left');

        // Join dengan tabel Simulator Konvensional
        $this->db->join('medis.tb_simulatorInformationDr',
            'medis.tb_simulatorInformationDr.id = medis.radiasi_eksterna.id_ctsim AND medis.radiasi_eksterna.category = "2"',
            'left');

        $this->db->where('pendaftaran.pendaftaran.NORM', $nomr);
        // Hanya ambil data dengan status aktif (bukan 0)
        $this->db->where('medis.radiasi_eksterna.status', 1);

        $i = 0;
        foreach ($this->column_search as $item) {
            if ($_GET['search']['value']) {
                if ($i === 0) {
                    $this->db->group_start();
                    $this->db->like($item, $_GET['search']['value']);
                } else {
                    $this->db->or_like($item, $_GET['search']['value']);
                }
                if (count($this->column_search) - 1 == $i)
                    $this->db->group_end();
            }
            $i++;
        }

        if (isset($_GET['order'])) {
            $this->db->order_by($this->column_order[$_GET['order']['0']['column']], $_GET['order']['0']['dir']);
        } else if (isset($this->order)) {
            $order = $this->order;
            $this->db->order_by(key($order), $order[key($order)]);
        }
    }

    function get_datatables($nomr)
    {
        $this->_get_datatables_query($nomr);
        if ($_GET['length'] != -1)
            $this->db->limit($_GET['length'], $_GET['start']);
        $query = $this->db->get();
        return $query->result();
    }

    function count_filtered($nomr)
    {
        $this->_get_datatables_query($nomr);
        $query = $this->db->get();
        return $query->num_rows();
    }

    public function count_all($nomr)
    {
        $this->db->from($this->table);
        $this->db->join('pendaftaran.kunjungan', 'pendaftaran.kunjungan.NOMOR = medis.radiasi_eksterna.nokun', 'left');
        $this->db->join('pendaftaran.pendaftaran', 'pendaftaran.pendaftaran.NOMOR = pendaftaran.kunjungan.NOPEN', 'left');
        $this->db->where('pendaftaran.pendaftaran.NORM', $nomr);
        // Hanya hitung data dengan status aktif (bukan 0)
        $this->db->where('medis.radiasi_eksterna.status', 1);
        return $this->db->count_all_results();
    }

    var $table_detail = 'medis.radiasi_eksterna_detail';
    var $column_order_detail = array(null, 'tanggal', 'energi', 'ssd_sad', 'dosis_fraksi', 'oleh');
    var $column_search_detail = array('tanggal', 'energi_display', 'ssd_sad', 'dosis_fraksi', 'nama_rtt');
    var $order_detail = array('id' => 'asc');

    private function _get_datatables_query_detail($id_radiasi)
    {
        $this->db->select([
            'medis.radiasi_eksterna_detail.*',
            'COALESCE(db_master.variabel.variabel, medis.radiasi_eksterna_detail.energi) as energi_display',
            'master.getNamaLengkapPegawai(ap.NIP) as nama_rtt'
        ]);
        $this->db->from($this->table_detail);
        $this->db->join('db_master.variabel', 'db_master.variabel.id_variabel = medis.radiasi_eksterna_detail.energi AND db_master.variabel.id_referensi = 1862', 'left');
        $this->db->join('aplikasi.pengguna ap', 'ap.ID = medis.radiasi_eksterna_detail.oleh', 'left');
        $this->db->where('medis.radiasi_eksterna_detail.id_radiasi', $id_radiasi);
        // Hanya ambil data dengan status aktif (bukan 0)
        $this->db->where('medis.radiasi_eksterna_detail.status', 1);

        $i = 0;
        foreach ($this->column_search_detail as $item) {
            if ($_GET['search']['value']) {
                if ($i === 0) {
                    $this->db->group_start();
                    // Untuk kolom energi_display, gunakan ekspresi asli dari SELECT
                    if ($item == 'energi_display') {
                        $this->db->where("LOWER(COALESCE(db_master.variabel.variabel, medis.radiasi_eksterna_detail.energi)) LIKE", '%' . strtolower($_GET['search']['value']) . '%');
                    }
                    // Untuk kolom nama_rtt, gunakan ekspresi asli dari SELECT
                    elseif ($item == 'nama_rtt') {
                        $this->db->where("LOWER(master.getNamaLengkapPegawai(ap.NIP)) LIKE", '%' . strtolower($_GET['search']['value']) . '%');
                    } else {
                        $this->db->like($item, $_GET['search']['value']);
                    }
                } else {
                    // Untuk kolom energi_display, gunakan ekspresi asli dari SELECT
                    if ($item == 'energi_display') {
                        $this->db->or_where("LOWER(COALESCE(db_master.variabel.variabel, medis.radiasi_eksterna_detail.energi)) LIKE", '%' . strtolower($_GET['search']['value']) . '%');
                    }
                    // Untuk kolom nama_rtt, gunakan ekspresi asli dari SELECT
                    elseif ($item == 'nama_rtt') {
                        $this->db->or_where("LOWER(master.getNamaLengkapPegawai(ap.NIP)) LIKE", '%' . strtolower($_GET['search']['value']) . '%');
                    } else {
                        $this->db->or_like($item, $_GET['search']['value']);
                    }
                }
                if (count($this->column_search_detail) - 1 == $i)
                    $this->db->group_end();
            }
            $i++;
        }

        if (isset($_GET['order'])) {
            $this->db->order_by($this->column_order_detail[$_GET['order']['0']['column']], $_GET['order']['0']['dir']);
        } else if (isset($this->order_detail)) {
            $order = $this->order_detail;
            $this->db->order_by(key($order), $order[key($order)]);
        }
    }

    function get_datatables_detail($id_radiasi)
    {
        $this->_get_datatables_query_detail($id_radiasi);
        if ($_GET['length'] != -1)
            $this->db->limit($_GET['length'], $_GET['start']);
        $query = $this->db->get();
        return $query->result();
    }

    function count_filtered_detail($id_radiasi)
    {
        $this->_get_datatables_query_detail($id_radiasi);
        $query = $this->db->get();
        return $query->num_rows();
    }

    public function count_all_detail($id_radiasi)
    {
        $this->db->from($this->table_detail);
        $this->db->where('id_radiasi', $id_radiasi);
        // Hanya hitung data dengan status aktif (bukan 0)
        $this->db->where('status', 1);
        return $this->db->count_all_results();
    }

    public function save($data)
    {
        $this->db->insert($this->table, $data);
        return $this->db->insert_id();
    }

    public function save_detail($data)
    {
        $this->db->insert($this->table_detail, $data);
        return $this->db->insert_id();
    }

    /**
     * Get data radiasi eksterna by ID
     */
    public function get_by_id($id)
    {
        $this->db->select([
            'medis.radiasi_eksterna.*',
            'master.pasien.NAMA as nama_pasien',
            'master.pasien.NORM as nomr',
            'master.pasien.TANGGAL_LAHIR as tanggal_lahir',
            'CASE
                WHEN medis.radiasi_eksterna.category = "1" THEN CONCAT("CT - ", medis.tb_ctSimulatorDokter.deskripsi)
                WHEN medis.radiasi_eksterna.category = "2" THEN CONCAT("SK - ", medis.tb_simulatorInformationDr.diagnosa)
                ELSE "Tidak ada catatan"
            END as catatan_simulator'
        ]);
        $this->db->from($this->table);
        $this->db->join('pendaftaran.kunjungan', 'pendaftaran.kunjungan.NOMOR = medis.radiasi_eksterna.nokun', 'left');
        $this->db->join('pendaftaran.pendaftaran', 'pendaftaran.pendaftaran.NOMOR = pendaftaran.kunjungan.NOPEN', 'left');
        $this->db->join('master.pasien', 'master.pasien.NORM = pendaftaran.pendaftaran.NORM', 'left');

        // Join dengan tabel CT Simulator
        $this->db->join('medis.tb_ctSimulatorDokter',
            'medis.tb_ctSimulatorDokter.id = medis.radiasi_eksterna.id_ctsim AND medis.radiasi_eksterna.category = "1"',
            'left');

        // Join dengan tabel Simulator Konvensional
        $this->db->join('medis.tb_simulatorInformationDr',
            'medis.tb_simulatorInformationDr.id = medis.radiasi_eksterna.id_ctsim AND medis.radiasi_eksterna.category = "2"',
            'left');

        $this->db->where('medis.radiasi_eksterna.id', $id);
        // Hanya ambil data dengan status aktif (bukan 0)
        $this->db->where('medis.radiasi_eksterna.status', 1);
        $query = $this->db->get();
        return $query->row();
    }

    /**
     * Get data radiasi eksterna detail by ID
     */
    public function get_detail_by_id($id)
    {
        $this->db->where('id', $id);
        $this->db->where('status', 1);
        $query = $this->db->get($this->table_detail);
        return $query->row();
    }

    /**
     * Update data radiasi eksterna
     */
    public function update($id, $data)
    {
        $this->db->where('id', $id);
        return $this->db->update($this->table, $data);
    }

    /**
     * Update data radiasi eksterna detail
     */
    public function update_detail($id, $data)
    {
        $this->db->where('id', $id);
        return $this->db->update($this->table_detail, $data);
    }

    /**
     * Soft delete radiasi eksterna
     */
    public function delete($id)
    {
        $this->db->trans_start();
        // Soft delete master record
        $this->db->where('id', $id);
        $this->db->update($this->table, ['status' => 0]);

        // Soft delete detail records
        $this->db->where('id_radiasi', $id);
        $this->db->update($this->table_detail, ['status' => 0]);
        
        $this->db->trans_complete();

        return $this->db->trans_status();
    }

    /**
     * Soft delete radiasi eksterna detail
     */
    public function delete_detail($id)
    {
        $data = array('status' => 0);
        $this->db->where('id', $id);
        return $this->db->update($this->table_detail, $data);
    }

    public function get_simulator_data($nomr)
    {
        // Ambil data dari CT Simulator
        $this->db->select("id, nokun, CONCAT('CT - ', deskripsi) as text, '1' as category");
        $this->db->from('medis.tb_ctSimulatorDokter');
        $this->db->join('pendaftaran.kunjungan', 'pendaftaran.kunjungan.NOMOR = medis.tb_ctSimulatorDokter.nokun', 'left');
        $this->db->join('pendaftaran.pendaftaran', 'pendaftaran.pendaftaran.NOMOR = pendaftaran.kunjungan.NOPEN', 'left');
        $this->db->where('pendaftaran.pendaftaran.NORM', $nomr);
        $ct_data = $this->db->get()->result_array();

        // Ambil data dari Simulator Konvensional
        $this->db->select("id, nokun, CONCAT('SK - ', diagnosa) as text, '2' as category");
        $this->db->from('medis.tb_simulatorInformationDr');
        $this->db->join('pendaftaran.kunjungan', 'pendaftaran.kunjungan.NOMOR = medis.tb_simulatorInformationDr.nokun', 'left');
        $this->db->join('pendaftaran.pendaftaran', 'pendaftaran.pendaftaran.NOMOR = pendaftaran.kunjungan.NOPEN', 'left');
        $this->db->where('pendaftaran.pendaftaran.NORM', $nomr);
        $sk_data = $this->db->get()->result_array();

        // Gabungkan hasil
        $result = array_merge($ct_data, $sk_data);

        return $result;
    }

    /**
     * Check table structure untuk debugging
     */
    public function check_table_structure()
    {
        $query = $this->db->query("DESCRIBE medis.radiasi_eksterna");
        return $query->result_array();
    }

    /**
     * Add missing columns if they don't exist
     */
    public function add_missing_columns()
    {
        // Check if id_ctsim column exists
        $check_id_ctsim = $this->db->query("SHOW COLUMNS FROM medis.radiasi_eksterna LIKE 'id_ctsim'");
        if ($check_id_ctsim->num_rows() == 0) {
            $this->db->query("ALTER TABLE medis.radiasi_eksterna ADD COLUMN id_ctsim INT NULL AFTER lokasi");
        }

        // Check if category column exists
        $check_category = $this->db->query("SHOW COLUMNS FROM medis.radiasi_eksterna LIKE 'category'");
        if ($check_category->num_rows() == 0) {
            $this->db->query("ALTER TABLE medis.radiasi_eksterna ADD COLUMN category VARCHAR(10) NULL AFTER id_ctsim");
        }

        return true;
    }

    /**
     * Get data energi dari tabel db_master.variabel untuk Select2
     */
    public function get_energi_options($search = '', $id = null)
    {
        $this->db->select('id_variabel, variabel');
        $this->db->from('db_master.variabel');
        $this->db->where('id_referensi', 1862);
        $this->db->where('status', 1);

        if (!empty($search)) {
            $this->db->like('variabel', $search);
        }

        if (!empty($id)) {
            $this->db->where('id_variabel', $id);
        }

        $this->db->order_by('seq ASC, variabel ASC');
        $query = $this->db->get();

        return $query->result();
    }
}
