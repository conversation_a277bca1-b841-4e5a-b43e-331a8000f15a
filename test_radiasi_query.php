<?php
/**
 * Test file untuk memverifikasi query radiasi eksterna
 * Jalankan file ini untuk test query sebelum implementasi
 */

// Simulasi query yang diperbaiki
$test_query = "
SELECT 
    medis.radiasi_eksterna.*,
    CASE
        WHEN medis.radiasi_eksterna.category = '1' THEN CONCAT('CT - ', medis.tb_ctSimulatorDokter.deskripsi)
        WHEN medis.radiasi_eksterna.category = '2' THEN CONCAT('SK - ', medis.tb_simulatorInformationDr.diagnosa)
        ELSE 'Tidak ada catatan'
    END as catatan_simulator
FROM medis.radiasi_eksterna
LEFT JOIN pendaftaran.kunjungan ON pendaftaran.kunjungan.NOMOR = medis.radiasi_eksterna.nokun
LEFT JOIN pendaftaran.pendaftaran ON pendaftaran.pendaftaran.NOMOR = pendaftaran.kunjungan.NOPEN
LEFT JOIN medis.tb_ctSimulatorDokter ON medis.tb_ctSimulatorDokter.id = medis.radiasi_eksterna.id_ctsim AND medis.radiasi_eksterna.category = '1'
LEFT JOIN medis.tb_simulatorInformationDr ON medis.tb_simulatorInformationDr.id = medis.radiasi_eksterna.id_ctsim AND medis.radiasi_eksterna.category = '2'
WHERE pendaftaran.pendaftaran.NORM = '308650'
AND medis.radiasi_eksterna.status = 1
AND (
    lokasi LIKE '%nge%' ESCAPE '!'
    OR (CASE
        WHEN medis.radiasi_eksterna.category = '1' THEN CONCAT('CT - ', medis.tb_ctSimulatorDokter.deskripsi)
        WHEN medis.radiasi_eksterna.category = '2' THEN CONCAT('SK - ', medis.tb_simulatorInformationDr.diagnosa)
        ELSE 'Tidak ada catatan'
    END) LIKE '%nge%'
    OR oleh LIKE '%nge%' ESCAPE '!'
    OR waktu LIKE '%nge%' ESCAPE '!'
)
LIMIT 10";

echo "Query yang diperbaiki:\n";
echo $test_query;
echo "\n\n";

echo "Perbaikan yang dilakukan:\n";
echo "1. Mengganti 'catatan_simulator' dengan ekspresi CASE lengkap dalam WHERE clause\n";
echo "2. Memastikan JOIN dengan tabel tb_ctSimulatorDokter dan tb_simulatorInformationDr sudah ada\n";
echo "3. Menggunakan kondisi yang sama dengan SELECT untuk konsistensi\n";
?>
